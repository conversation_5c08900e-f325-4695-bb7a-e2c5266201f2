/* eslint-disable max-lines */
/* eslint-disable complexity */
import { useState, ReactNode } from 'react';
import {
  AppShell,
  Burger,
  Group,
  NavLink,
  Text,
  useMantineColorScheme,
  Box,
  ActionIcon,
  Title,
  Avatar,
  Button,
  Badge,
  Menu,
  Switch,
  Stack,
} from '@mantine/core';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  IconDashboard,
  IconUserCircle,
  IconLogout,
  IconSun,
  IconMoonStars,
  IconPackage,
  IconQrcode,
  IconClipboardList,
  IconTruck,
  IconChevronDown,
} from '@tabler/icons-react';
import { useIsClient } from '../../hooks/useIsClient';
import { useShipmentCounts } from '../../hooks/useShipmentCounts';
import { useTokenExpiration } from '../../hooks/useTokenExpiration';
import { ROUTES } from '../../data/routes';
import LanguageSwitcher from '../common/LanguageSwitcher';
import { NotificationDropdown } from '../notifications';
import { ProgressBar } from '../common/ProgressBar';
import {
  ACCESS_OPERATOR_USER_TYPE, CAR_OPERATOR_USER_TYPE, CUSTOMER_USER_TYPE, LocaleCookie,
} from '../../data';
import { useMediaQuery } from '@mantine/hooks';
import { setCookie } from 'cookies-next';

interface AppLayoutProps {
  children: ReactNode;
}

const getUserDashboardPath = (userType?: string | null): string => {
  switch (userType) {
    case 'CUSTOMER':
      return '/dashboard/customer';
    case 'CAR_OPERATOR':
      return '/dashboard/car-operator';
    case 'ACCESS_OPERATOR':
      return '/dashboard/access-operator';
    default:
      return ROUTES.root;
  }
};

// Helper function to get theme colors - using CSS variables for better SSR compatibility
interface ThemeColors {
  textColor: string;
  mutedTextColor: string;
  activeBgColor: string;
  activeTextColor: string;
}

const getThemeColors = (colorScheme: string): ThemeColors => ({
  textColor: colorScheme === 'dark' ? 'var(--mantine-color-dark-0)' : 'var(--mantine-color-gray-8)',
  mutedTextColor: colorScheme === 'dark' ? 'var(--mantine-color-dark-2)' : 'var(--mantine-color-gray-6)',
  activeBgColor: colorScheme === 'dark' ? 'var(--mantine-color-dark-5)' : 'var(--mantine-color-blue-0)',
  activeTextColor: colorScheme === 'dark' ? 'var(--mantine-color-blue-1)' : 'var(--mantine-color-blue-7)',
});

// Helper component for user info with dropdown menu
interface UserInfoProps {
  session: { user?: { name?: string | null; email?: string | null; image?: string | null } };
  colorScheme: string;
  colors: ThemeColors;
  logoutText: string;
  toggleColorScheme: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  router: any;
  t: (key: string) => string;
}

function UserInfo({
  session, colorScheme, colors, logoutText, toggleColorScheme, router, t,
}: UserInfoProps) {
  const currentLocale = router.locale || 'ar';
  const isRTL = currentLocale === 'ar';

  const switchLanguage = (locale: string) => {
    // Set cookie first to persist the choice
    setCookie(LocaleCookie, locale, {
      maxAge: 365 * 24 * 60 * 60, // 1 year
      sameSite: 'lax',
    });
    // Then navigate to the new locale
    router.push(router.asPath, router.asPath, { locale });
  };

  return (
    <Menu shadow="md" width={200} position={isRTL ? 'bottom-start' : 'bottom-end'}>
      <Menu.Target>
        <Group gap="xs" style={{ cursor: 'pointer' }}>
          <Avatar src={session.user?.image} alt={session.user?.name || 'User Avatar'} radius="xl" size={32}>
            {session.user?.name?.charAt(0).toUpperCase() || session.user?.email?.charAt(0).toUpperCase()}
          </Avatar>
          {!useMediaQuery('(max-width: 48em)') && (
            <Text style={{ color: colors.textColor, fontWeight: 500 }}>
              {session.user?.name || session.user?.email}
            </Text>
          )}
          <IconChevronDown size="1rem" style={{ color: colors.mutedTextColor }} />
        </Group>
      </Menu.Target>

      <Menu.Dropdown>
        {/* Language Section */}
        <Menu.Label>{t('language')}</Menu.Label>
        <Box p="md">
          <Stack gap="xs" align="center">
            <Group justify="space-between" w="100%" style={{ fontSize: '14px' }}>
              <Text
                size="sm"
                fw={currentLocale === 'ar' ? 600 : 400}
                c={currentLocale === 'ar' ? 'blue' : 'dimmed'}
              >
                العربية
              </Text>
              <Switch
                checked={currentLocale === 'en'}
                onChange={(event) => switchLanguage(event.currentTarget.checked ? 'en' : 'ar')}
                size="md"
                color="blue"
                thumbIcon={
                  currentLocale === 'en' ? (
                    <Text size="xs" fw={700} c="blue">EN</Text>
                  ) : (
                    <Text size="xs" fw={700} c="blue">ع</Text>
                  )
                }
                styles={{
                  track: {
                    backgroundColor: currentLocale === 'ar' ? 'var(--mantine-color-blue-light)' : undefined,
                    borderColor: 'var(--mantine-color-blue-6)',
                  },
                  thumb: {
                    backgroundColor: 'white',
                    borderColor: 'var(--mantine-color-blue-6)',
                  },
                }}
              />
            </Group>
          </Stack>
        </Box>

        <Menu.Divider />

        {/* Theme Section */}
        <Menu.Label>{t('theme')}</Menu.Label>
        <Menu.Item
          leftSection={colorScheme === 'dark' ? <IconSun size="0.9rem" /> : <IconMoonStars size="0.9rem" />}
          onClick={toggleColorScheme}
          style={{
            padding: '8px 12px',
            fontSize: '14px',
          }}
        >
          {colorScheme === 'dark' ? t('light') : t('dark')}
        </Menu.Item>

        <Menu.Divider />

        {/* Logout Section */}
        <Menu.Item
          leftSection={<IconLogout size="0.9rem" />}
          color="red"
          onClick={() => signOut({ callbackUrl: '/' })}
          style={{
            padding: '8px 12px',
            fontSize: '14px',
          }}
        >
          {logoutText}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
}

function getProfileLinks(isAuthenticated: boolean, t: (key: string) => string) {
  return isAuthenticated ? [
    { icon: IconUserCircle, label: t('profile'), href: ROUTES.profile },
  ] : [];
}

function getCustomerShipmentLinks(isAuthenticated: boolean, userType: string | null | undefined, t: (key: string) => string) {
  return isAuthenticated && userType === CUSTOMER_USER_TYPE ? [
    { icon: IconPackage, label: t('myShipments'), href: ROUTES.customer.shipments },
  ] : [];
}

function getQRCodeLinks(isAuthenticated: boolean, userType: string | null | undefined, t: (key: string) => string) {
  return isAuthenticated && userType === ACCESS_OPERATOR_USER_TYPE ? [
    { icon: IconQrcode, label: t('qrGenerator'), href: '/qr-generator' },
  ] : [];
}

function getAOShipmentLinks(isAuthenticated: boolean, userType: string | null | undefined, pendingCount: number, incomingCount: number, t: (key: string) => string) {
  return isAuthenticated && userType === ACCESS_OPERATOR_USER_TYPE ? [
    {
      icon: IconPackage,
      label: t('pending'),
      href: ROUTES.accessOperator.pendingShipments,
      badge: pendingCount > 0 ? pendingCount : undefined,
    },
    {
      icon: IconTruck,
      label: t('incoming'),
      href: ROUTES.accessOperator.destShipments,
      badge: incomingCount > 0 ? incomingCount : undefined,
      badgeColor: 'cyan',
    },
    {
      icon: IconClipboardList,
      label: t('myShipments'),
      href: ROUTES.accessOperator.myShipments,
    },
  ] : [];
}

function getCOShipmentLinks(isAuthenticated: boolean, userType: string | null | undefined, availableCount: number, inTransitCount: number, t: (key: string) => string) {
  return isAuthenticated && userType === CAR_OPERATOR_USER_TYPE ? [
    {
      icon: IconClipboardList,
      label: t('availableShipments'),
      href: ROUTES.carOperator.myShipments,
      badge: availableCount > 0 ? availableCount : undefined,
      badgeColor: 'blue',
    },
    {
      icon: IconTruck,
      label: t('inTransitShipments'),
      href: ROUTES.carOperator.inTransitShipments,
      badge: inTransitCount > 0 ? inTransitCount : undefined,
      badgeColor: 'cyan',
    },
  ] : [];
}

export default function AppLayout({ children }: AppLayoutProps) {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme();
  const [opened, setOpened] = useState(false);
  const { data: session } = useSession();
  const router = useRouter();
  const isClient = useIsClient();
  const { isAuthenticated } = useTokenExpiration();
  const isAuthenticatedAndClient = isAuthenticated && isClient;
  const { t } = useTranslation('common');
  const {
    pendingCount,
    incomingCount,
    availableCount,
    inTransitCount,
  } = useShipmentCounts();
  const colors = getThemeColors(colorScheme);
  const isRTL = router.locale === 'ar';

  const mainLinks = [
    {
      icon: IconDashboard,
      label: t('dashboard'),
      href: isAuthenticatedAndClient && session?.user?.user_type
        ? getUserDashboardPath(session.user.user_type)
        : ROUTES.root,
    },
  ];

  // Use only the new helper-based declarations
  const profileLinks = getProfileLinks(isAuthenticatedAndClient, t);
  const shipmentLinks = getCustomerShipmentLinks(isAuthenticatedAndClient, session?.user?.user_type, t);
  const qrCodeLinks = getQRCodeLinks(isAuthenticatedAndClient, session?.user?.user_type, t);
  const aoShipmentLinks = getAOShipmentLinks(isAuthenticatedAndClient, session?.user?.user_type, pendingCount, incomingCount, t);
  const coShipmentLinks = getCOShipmentLinks(isAuthenticatedAndClient, session?.user?.user_type, availableCount, inTransitCount, t);
  const mapLinks: typeof mainLinks = [];

  const createNavLinks = (links: typeof mainLinks) => links.map((link) => (
    <NavLink
      key={link.label}
      label={link.label}
      leftSection={<link.icon size="1rem" stroke={1.5} />}
      component={Link}
      href={link.href}
      active={router.pathname === link.href || router.pathname.startsWith(`${link.href}/`)}
      onClick={() => setOpened(false)}
      styles={{
        root: {
          color: (router.pathname === link.href || router.pathname.startsWith(`${link.href}/`)) ? colors.activeTextColor : colors.textColor,
          backgroundColor: (router.pathname === link.href || router.pathname.startsWith(`${link.href}/`)) ? colors.activeBgColor : 'transparent',
          borderRadius: 'var(--mantine-radius-sm)',
          '&:hover': {
            backgroundColor: colorScheme === 'dark' ? 'var(--mantine-color-dark-6)' : 'var(--mantine-color-gray-1)',
          },
        },
        label: {
          fontWeight: (router.pathname === link.href || router.pathname.startsWith(`${link.href}/`)) ? 600 : 400,
        },
      }}
    />
  ));

  // Special function for links with badges
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const createNavLinksWithBadge = (links: Array<{ icon: React.ComponentType<any>; label: string; href: string; badge?: number; badgeColor?: string }>) => (
    links.map((link) => (
      <NavLink
        key={link.label}
        label={(
          <Group justify="space-between" w="100%">
            <Text>{link.label}</Text>
            {link.badge && link.badge > 0 && (
              <Badge size="sm" color={link.badgeColor || 'red'} variant="filled">
                {link.badge}
              </Badge>
            )}
          </Group>
        )}
        leftSection={<link.icon size="1rem" stroke={1.5} />}
        component={Link}
        href={link.href}
        active={router.pathname === link.href || router.pathname.startsWith(`${link.href}/`)}
        onClick={() => setOpened(false)}
        styles={{
          root: {
            color: (router.pathname === link.href || router.pathname.startsWith(`${link.href}/`)) ? colors.activeTextColor : colors.textColor,
            backgroundColor: (router.pathname === link.href || router.pathname.startsWith(`${link.href}/`)) ? colors.activeBgColor : 'transparent',
            borderRadius: 'var(--mantine-radius-sm)',
            '&:hover': {
              backgroundColor: colorScheme === 'dark' ? 'var(--mantine-color-dark-6)' : 'var(--mantine-color-gray-1)',
            },
          },
          label: {
            fontWeight: (router.pathname === link.href || router.pathname.startsWith(`${link.href}/`)) ? 600 : 400,
          },
        }}
      />
    )));

  const mainNavLinks = createNavLinks(mainLinks);
  const profileNavLinks = createNavLinks(profileLinks);
  const shipmentNavLinks = createNavLinks(shipmentLinks);
  const qrCodeNavLinks = createNavLinks(qrCodeLinks);
  const aoShipmentNavLinks = createNavLinksWithBadge(aoShipmentLinks);
  const coShipmentNavLinks = createNavLinksWithBadge(coShipmentLinks);
  const mapNavLinks = createNavLinks(mapLinks);

  return (
    <AppShell
      header={{ height: 70 }}
      navbar={{
        width: { sm: 200, lg: 250 },
        breakpoint: 'lg',
        collapsed: { mobile: !opened },
      }}
      styles={{
        header: {
          zIndex: 9999, // Ensure header appears above maps and other content
        },
        navbar: {
          zIndex: 9998, // Navbar should be below header but above content
        },
      }}
    >
      <ProgressBar />
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          {isRTL ? (
            <>
              {/* User actions section - appears on left in RTL */}
              <Group>
                {/* Show language switcher and dark mode toggle for non-authenticated users */}
                {!isAuthenticatedAndClient && (
                  <>
                    <LanguageSwitcher />
                    <ActionIcon
                      variant="default"
                      onClick={() => toggleColorScheme()}
                      size={36}
                      aria-label="Toggle color scheme"
                    >
                      {colorScheme === 'dark' ? (
                        <IconSun size="1.2rem" />
                      ) : (
                        <IconMoonStars size="1.2rem" />
                      )}
                    </ActionIcon>
                  </>
                )}
                {isAuthenticatedAndClient && session?.user && (
                  <NotificationDropdown />
                )}
                {isAuthenticatedAndClient && session?.user ? (
                  <UserInfo
                    session={session}
                    colorScheme={colorScheme}
                    colors={colors}
                    logoutText={t('logout')}
                    toggleColorScheme={toggleColorScheme}
                    router={router}
                    t={t}
                  />
                ) : (
                  <Button component={Link} href="/auth/login" variant="default">
                    {t('login')}
                  </Button>
                )}
              </Group>

              {/* Brand section - appears on right in RTL */}
              <Group>
                <Link href="/" style={{ textDecoration: 'none' }}>
                  <Title order={3} style={{ color: colors.textColor }}>
                    NAQALAT
                  </Title>
                </Link>
                <Box hiddenFrom="lg">
                  <Burger
                    opened={opened}
                    onClick={() => setOpened((o) => !o)}
                    size="sm"
                    color={colors.mutedTextColor}
                  />
                </Box>
              </Group>
            </>
          ) : (
            <>
              {/* Brand section - appears on left in LTR */}
              <Group>
                <Box hiddenFrom="lg">
                  <Burger
                    opened={opened}
                    onClick={() => setOpened((o) => !o)}
                    size="sm"
                    color={colors.mutedTextColor}
                  />
                </Box>
                <Link href="/" style={{ textDecoration: 'none' }}>
                  <Title order={3} style={{ color: colors.textColor }}>
                    NAQALAT
                  </Title>
                </Link>
              </Group>

              {/* User actions section - appears on right in LTR */}
              <Group>
                {/* Show language switcher and dark mode toggle for non-authenticated users */}
                {!isAuthenticatedAndClient && (
                  <>
                    <LanguageSwitcher />
                    <ActionIcon
                      variant="default"
                      onClick={() => toggleColorScheme()}
                      size={36}
                      aria-label="Toggle color scheme"
                    >
                      {colorScheme === 'dark' ? (
                        <IconSun size="1.2rem" />
                      ) : (
                        <IconMoonStars size="1.2rem" />
                      )}
                    </ActionIcon>
                  </>
                )}
                {isAuthenticatedAndClient && session?.user && (
                  <NotificationDropdown />
                )}
                {isAuthenticatedAndClient && session?.user ? (
                  <UserInfo
                    session={session}
                    colorScheme={colorScheme}
                    colors={colors}
                    logoutText={t('logout')}
                    toggleColorScheme={toggleColorScheme}
                    router={router}
                    t={t}
                  />
                ) : (
                  <Button component={Link} href="/auth/login" variant="default">
                    {t('login')}
                  </Button>
                )}
              </Group>
            </>
          )}
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p="md">
        <Text size="xs" tt="uppercase" fw={700} c={colors.mutedTextColor} mb="sm">
          {t('menu')}
        </Text>
        {mainNavLinks}

        {/* Profile Section */}
        {isAuthenticatedAndClient && profileNavLinks.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c={colors.mutedTextColor} mb="sm" mt="lg">
              {t('profile')}
            </Text>
            {profileNavLinks}
          </>
        )}

        {/* Shipments Section - Only for Customers */}
        {isAuthenticatedAndClient && shipmentNavLinks.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c={colors.mutedTextColor} mb="sm" mt="lg">
              {t('shipments')}
            </Text>
            {shipmentNavLinks}
          </>
        )}

        {/* QR Codes Section - Only for Access Operators */}
        {isAuthenticatedAndClient && qrCodeNavLinks.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c={colors.mutedTextColor} mb="sm" mt="lg">
              {t('qrCodes')}
            </Text>
            {qrCodeNavLinks}
          </>
        )}

        {/* AO Shipments Section - Only for Access Operators */}
        {isAuthenticatedAndClient && aoShipmentNavLinks.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c={colors.mutedTextColor} mb="sm" mt="lg">
              {t('shipments')}
            </Text>
            {aoShipmentNavLinks}
          </>
        )}

        {/* CO Shipments Section - Only for Car Operators */}
        {isAuthenticatedAndClient && coShipmentNavLinks.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c={colors.mutedTextColor} mb="sm" mt="lg">
              {t('shipments')}
            </Text>
            {coShipmentNavLinks}
          </>
        )}

        {/* Map Section */}
        {isAuthenticatedAndClient && mapNavLinks.length > 0 && (
          <>
            <Text size="xs" tt="uppercase" fw={700} c={colors.mutedTextColor} mb="sm" mt="lg">
              {t('maps')}
            </Text>
            {mapNavLinks}
          </>
        )}
      </AppShell.Navbar>

      <AppShell.Main mt={10} pb={10}>
        {children}
      </AppShell.Main>
    </AppShell>
  );
}
